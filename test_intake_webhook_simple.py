#!/usr/bin/env python3
"""
Simple integration test for the intake webhook endpoint.

This test verifies the core functionality without requiring the full app setup.
"""

import json
import os
from uuid import uuid4

from backend.utils.hmac_utils import generate_hmac_signature, verify_hmac_signature
from backend.api.schemas.intake_webhook import IntakeEventRequest, IntakeEventResponse


def test_hmac_functionality():
    """Test HMAC signature generation and verification."""
    print("Testing HMAC functionality...")
    
    payload = b"test payload"
    secret = "test_secret_key_123"
    
    # Generate signature
    signature = generate_hmac_signature(payload, secret)
    print(f"Generated signature: {signature}")
    
    # Verify signature
    is_valid = verify_hmac_signature(payload, signature, secret)
    assert is_valid, "HMAC verification should pass with correct secret"
    
    # Test with wrong secret
    is_invalid = verify_hmac_signature(payload, signature, "wrong_secret")
    assert not is_invalid, "HMAC verification should fail with wrong secret"
    
    print("✅ HMAC functionality test passed")


def test_schema_validation():
    """Test Pydantic schema validation."""
    print("Testing schema validation...")
    
    # Valid payload
    valid_payload = {
        "tenant_id": str(uuid4()),
        "call_id": "call_123456",
        "call_status": "completed",
        "call_duration": 300,
        "transcript": "Hello, I was in a car accident last week...",
        "summary": "Client reports car accident, seeking legal representation",
        "client_info": {
            "name": "John Doe",
            "phone": "+1234567890",
            "email": "<EMAIL>"
        },
        "case_info": {
            "type": "personal_injury",
            "incident_date": "2025-01-01",
            "description": "Rear-end collision at intersection"
        },
        "metadata": {
            "call_timestamp": "2025-01-03T10:00:00Z",
            "agent_version": "v1.2.3"
        }
    }
    
    # Test request schema
    request = IntakeEventRequest(**valid_payload)
    assert request.tenant_id is not None
    assert request.call_id == "call_123456"
    assert request.call_status == "completed"
    assert request.call_duration == 300
    
    # Test response schema
    response = IntakeEventResponse(event_id=uuid4())
    assert response.status == "queued"
    assert response.message == "Event queued for processing"
    
    print("✅ Schema validation test passed")


def test_end_to_end_payload():
    """Test end-to-end payload processing."""
    print("Testing end-to-end payload processing...")
    
    # Create sample payload
    payload_data = {
        "tenant_id": str(uuid4()),
        "call_id": "call_789012",
        "call_status": "completed",
        "call_duration": 450,
        "transcript": "I need help with a slip and fall case...",
        "summary": "Client injured in slip and fall incident",
        "client_info": {
            "name": "Jane Smith",
            "phone": "+1987654321"
        },
        "case_info": {
            "type": "premises_liability"
        },
        "metadata": {}
    }
    
    # Convert to JSON
    payload_json = json.dumps(payload_data)
    payload_bytes = payload_json.encode('utf-8')
    
    # Generate HMAC signature
    secret = "test_secret_key_123"
    signature = generate_hmac_signature(payload_bytes, secret)
    
    # Verify signature
    is_valid = verify_hmac_signature(payload_bytes, signature, secret)
    assert is_valid, "Signature verification should pass"
    
    # Validate schema
    request = IntakeEventRequest(**payload_data)
    assert request.call_id == "call_789012"
    
    print("✅ End-to-end payload test passed")


def test_webhook_security():
    """Test webhook security features."""
    print("Testing webhook security...")
    
    payload = b'{"tenant_id":"123","call_id":"test"}'
    secret = "super_secret_key"
    
    # Test signature with prefix
    signature_with_prefix = generate_hmac_signature(payload, secret)
    assert signature_with_prefix.startswith("sha256=")
    
    # Test verification with prefix
    is_valid_with_prefix = verify_hmac_signature(payload, signature_with_prefix, secret)
    assert is_valid_with_prefix
    
    # Test verification without prefix
    signature_without_prefix = signature_with_prefix.replace("sha256=", "")
    is_valid_without_prefix = verify_hmac_signature(payload, signature_without_prefix, secret)
    assert is_valid_without_prefix
    
    # Test empty signature
    is_invalid_empty = verify_hmac_signature(payload, "", secret)
    assert not is_invalid_empty
    
    # Test None signature
    is_invalid_none = verify_hmac_signature(payload, None, secret)
    assert not is_invalid_none
    
    print("✅ Webhook security test passed")


if __name__ == "__main__":
    print("🚀 Running intake webhook integration tests...\n")
    
    try:
        test_hmac_functionality()
        test_schema_validation()
        test_end_to_end_payload()
        test_webhook_security()
        
        print("\n🎉 All tests passed! The intake webhook implementation is working correctly.")
        print("\n📋 Summary:")
        print("  ✅ HMAC signature generation and verification")
        print("  ✅ Pydantic schema validation")
        print("  ✅ End-to-end payload processing")
        print("  ✅ Security features")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        raise
