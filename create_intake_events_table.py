#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create the intake_events table using Supabase connector.

This script creates the intake_events table in the tenants schema
and adds the necessary indexes for optimal performance.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Set up environment
from dotenv import load_dotenv
load_dotenv()

async def create_intake_events_table():
    """Create the intake_events table using Supabase connector."""
    
    try:
        # Import the Supabase client
        from src.pi_lawyer.db.supabase_client import get_supabase
        
        print("🔗 Connecting to Supabase...")
        supabase = get_supabase()
        
        # SQL to create the table and indexes
        create_table_sql = """
        -- Ensure tenants schema exists
        CREATE SCHEMA IF NOT EXISTS tenants;

        -- Create intake_events table
        CREATE TABLE IF NOT EXISTS tenants.intake_events (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            tenant_id UUID NOT NULL,
            payload JSONB NOT NULL,
            received_at TIMESTAMPTZ NOT NULL DEFAULT now(),
            processed BOOLEAN NOT NULL DEFAULT false
        );

        -- Create indexes for better query performance
        CREATE INDEX IF NOT EXISTS ix_tenants_intake_events_tenant_id 
            ON tenants.intake_events(tenant_id);

        CREATE INDEX IF NOT EXISTS ix_tenants_intake_events_received_at 
            ON tenants.intake_events(received_at);

        CREATE INDEX IF NOT EXISTS ix_tenants_intake_events_processed 
            ON tenants.intake_events(processed);

        -- Add comments for documentation
        COMMENT ON TABLE tenants.intake_events IS 'Stores webhook events from the Voice service for intake processing';
        COMMENT ON COLUMN tenants.intake_events.id IS 'Unique identifier for the intake event';
        COMMENT ON COLUMN tenants.intake_events.tenant_id IS 'Tenant ID for multi-tenant isolation';
        COMMENT ON COLUMN tenants.intake_events.payload IS 'Raw JSON payload from the Voice service webhook';
        COMMENT ON COLUMN tenants.intake_events.received_at IS 'Timestamp when the webhook was received';
        COMMENT ON COLUMN tenants.intake_events.processed IS 'Flag indicating if the event has been processed';
        """
        
        print("📋 Creating intake_events table...")
        
        # Execute the SQL using Supabase RPC
        result = supabase.rpc('exec_sql', {'sql': create_table_sql}).execute()
        
        if result.data is not None:
            print("✅ Table created successfully!")
        else:
            print(f"❌ Error creating table: {result}")
            return False
        
        # Verify table creation
        print("🔍 Verifying table creation...")
        
        verify_sql = """
        SELECT 
            schemaname,
            tablename,
            tableowner
        FROM pg_tables 
        WHERE schemaname = 'tenants' AND tablename = 'intake_events';
        """
        
        verify_result = supabase.rpc('exec_sql', {'sql': verify_sql}).execute()
        
        if verify_result.data and len(verify_result.data) > 0:
            print("✅ Table verification successful!")
            print(f"   Schema: {verify_result.data[0].get('schemaname')}")
            print(f"   Table: {verify_result.data[0].get('tablename')}")
            print(f"   Owner: {verify_result.data[0].get('tableowner')}")
        else:
            print("⚠️  Table verification failed - table may not exist")
        
        # Verify indexes
        print("🔍 Verifying indexes...")
        
        index_sql = """
        SELECT 
            indexname,
            indexdef
        FROM pg_indexes 
        WHERE schemaname = 'tenants' AND tablename = 'intake_events'
        ORDER BY indexname;
        """
        
        index_result = supabase.rpc('exec_sql', {'sql': index_sql}).execute()
        
        if index_result.data:
            print("✅ Indexes verified:")
            for idx in index_result.data:
                print(f"   - {idx.get('indexname')}")
        else:
            print("⚠️  No indexes found")
        
        print("\n🎉 Database setup completed successfully!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure you're running this from the project root directory")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


async def test_table_operations():
    """Test basic table operations."""
    
    try:
        from src.pi_lawyer.db.supabase_client import get_supabase
        from uuid import uuid4
        import json
        
        print("\n🧪 Testing table operations...")
        supabase = get_supabase()
        
        # Test data
        test_payload = {
            "tenant_id": str(uuid4()),
            "call_id": "test_call_123",
            "call_status": "completed",
            "call_duration": 300,
            "transcript": "Test transcript",
            "summary": "Test summary",
            "client_info": {"name": "Test Client"},
            "case_info": {"type": "test_case"},
            "metadata": {"test": True}
        }
        
        # Insert test record
        print("📝 Inserting test record...")
        insert_result = supabase.table('tenants.intake_events').insert({
            'tenant_id': test_payload['tenant_id'],
            'payload': test_payload,
            'processed': False
        }).execute()
        
        if insert_result.data:
            event_id = insert_result.data[0]['id']
            print(f"✅ Test record inserted with ID: {event_id}")
            
            # Query the record back
            print("🔍 Querying test record...")
            query_result = supabase.table('tenants.intake_events').select('*').eq('id', event_id).execute()
            
            if query_result.data:
                record = query_result.data[0]
                print("✅ Test record retrieved successfully!")
                print(f"   ID: {record['id']}")
                print(f"   Tenant ID: {record['tenant_id']}")
                print(f"   Processed: {record['processed']}")
                print(f"   Received At: {record['received_at']}")
                
                # Clean up test record
                print("🧹 Cleaning up test record...")
                delete_result = supabase.table('tenants.intake_events').delete().eq('id', event_id).execute()
                
                if delete_result.data:
                    print("✅ Test record cleaned up successfully!")
                else:
                    print("⚠️  Failed to clean up test record")
            else:
                print("❌ Failed to retrieve test record")
        else:
            print(f"❌ Failed to insert test record: {insert_result}")
            
    except Exception as e:
        print(f"❌ Test error: {e}")


if __name__ == "__main__":
    print("🚀 Starting intake_events table creation...\n")
    
    # Check environment variables
    required_vars = ["SUPABASE_URL", "SUPABASE_SERVICE_KEY"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
        print("Please set these in your .env file:")
        for var in missing_vars:
            print(f"   {var}=your_value_here")
        sys.exit(1)
    
    # Run the table creation
    success = asyncio.run(create_intake_events_table())
    
    if success:
        # Run tests
        asyncio.run(test_table_operations())
        print("\n✅ All operations completed successfully!")
    else:
        print("\n❌ Table creation failed!")
        sys.exit(1)
